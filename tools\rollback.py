
import requests
import json
from typing import Any, List

from langchain_core.tools import tool

from .cicd import HEADERS, BASE_URL, cicd_search, generate_select_service_form

@tool
def get_rollback_list(app_id: str, env: str, env_target: str, namespace: str, cluster: str, senv: str = "") -> str:
    """
    获取回滚列表
    Args:
        app_id: 应用ID
        env: 环境（dev/testing/staging/production)
        env_target: 目标环境类型（origin/sub/sub_v2）
        namespace: 命名空间
        cluster: 集群
        senv: 子环境名称，当env_target为sub_v2时必填
    Returns:
        回滚列表表单
    """ 
    try:
        # 构建API请求参数
        params = {
            "appId": app_id,
            "env": env,
            "namespace": namespace,
            "cluster": cluster,
            "envTarget": env_target,
        }
        
        # 子环境2.0
        if env_target == 'sub_v2':
            if not senv:
                return json.dumps({"type": "error", "message": "选择子环境时，必须提供子环境名称"})
            params["senv"] = senv
        
        # 构建URL查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        
        response = requests.get(
            f"{BASE_URL}deploy/runtime/rollback-list?{query_string}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        data = result.get("data", [])
        
        if not data or isinstance(data, str):
            error_msg = "当前环境无可回滚的版本。可能的原因：\n1. 该环境尚未有任何部署记录\n2. 环境配置参数不正确\n3. 服务在此环境中未运行\n\n建议：\n- 检查环境、集群、命名空间等参数是否正确\n- 确认服务是否已在该环境部署\n- 可以尝试其他环境或联系运维人员"
            return json.dumps({"type": "error", "message": error_msg})
        
        return generate_rollback_execution_form(cluster, namespace, data)
    except Exception as e:
        return json.dumps({"type": "error", "message": f"获取回滚列表失败: {str(e)}"})

@tool
def rollback(release_id: str, description: str = "agent回滚") -> str:
    """
    回滚
    Args:
        release_id: 发布ID
    Returns:
        回滚结果
    """
    operatorByEmployeeNo = "T2517"
    try:
        response = requests.post(
            f"{BASE_URL}deploy/runtime/openapi/rollback",
            json={
                "releaseId": int(release_id),
                "description": description,
                "operatorByEmployeeNo": operatorByEmployeeNo,
            },
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        return result
    except Exception as e:
        return f"执行回滚失败: {str(e)}"

@tool
def prepare_rollback_execution(service_name: str) -> str:
    """
    准备回滚执行，获取服务信息、环境参数和回滚版本列表
    Args:
        service_name: 服务名称或包含"用户已选择服务ID="的消息
    Returns:
        包含服务信息、环境选择和回滚版本的JSON格式表单，供用户选择
    """
    try:
        # 提取服务ID或使用服务名称
        service_id = None
        if "用户已选择服务ID=" in service_name:
            service_id = service_name.split("用户已选择服务ID=")[1].strip()
            response = requests.get(
                f"{BASE_URL}app/apps/{service_id}",
                headers=HEADERS,
                timeout=10,
            )
            result = response.json()
            service_info = result.get("data", {})
            if not service_info:
                return json.dumps({"type": "error", "message": f"无法获取服务ID为{service_id}的信息"})
        else:
            service_info = cicd_search(service_name)
            
            # 处理多个服务的情况
            if isinstance(service_info, str) and "查询到多个服务" in service_info:
                # 提取服务列表并创建选择表单
                import re
                import ast
                match = re.search(r'\[(.*)\]', service_info)
                if match:
                    services_str = "[" + match.group(1) + "]"
                    services_list = ast.literal_eval(services_str)
                    
                    return generate_select_service_form(services_list)
                else:
                    return json.dumps({"type": "error", "message": "无法提取服务列表"})
            
            if isinstance(service_info, str):
                return json.dumps({"type": "error", "message": service_info})

            print("service_info:" , service_info)
            
            service_id = service_info.get('id')

        return generate_rollback_version_form(service_id, service_info.get('name', service_name))

    except Exception as e:
        return json.dumps({"type": "error", "message": f"准备回滚操作失败: {str(e)}"})

@tool
def get_running_version(service_id: int, env: str, env_target: str, cluster: str, namespace: str, senv: str = "") -> str:
    """
        获取当前运行版本
        Args:
            service_id: 应用ID
            env: 环境（dev/testing/staging/production)
            env_target: 目标环境类型（origin/sub/sub_v2）
            cluster: 集群
            namespace: 命名空间
            senv: 子环境名称，当env_target为sub_v2时必填
        Returns:
            当前运行版本
    """
    try:
        params = {
            "appId": service_id,
            "env": env,
            "envTargetType": env_target,
            "cluster": cluster,
            "namespace": namespace,
        }
        if env_target == 'sub_v2':
            params["senv"] = senv
        
        # 构建URL查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        response = requests.get(
            f"{BASE_URL}deploy/deploy-configs/changelogs/running/detail?{query_string}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        return result.get("data", None)
    except Exception as e:
        return f"获取当前运行版本失败: {str(e)}"
        


def generate_rollback_version_form(service_id: int, service_name: str):
    form_dict = {
        "type": "formSchema",
        "schema": {
            "title": f'选择{service_name}服务回滚的环境',
            "type": "object",
            "required": [
                "env",
                "env_target",
                "cluster",
                "namespace"
            ],
            "properties": {
                "env": {
                    "type": "string",
                    "title": "环境选择",
                    "enum": ["dev", "testing", "staging", "production"]
                },
                "env_target": {
                    "type": "string",
                    "title": "目标环境类型",
                    "enum": ["origin", "sub", "sub_v2"],
                    "default": "origin"
                },
                "senv": {
                    "type": "string",
                    "title": "子环境名称",
                    "description": "当选择子环境时必填"
                },
                "cluster": {
                    "type": "string",
                    "title": "集群名称"
                },
                "namespace": {
                    "type": "string",
                    "title": "命名空间"
                }
            }
        },
        "uiSchema": {
            "env": {
                "ui:description": "环境选择",
                "ui:enumNames": ["开发环境", "测试环境", "预发布环境", "生产环境"]
            },
            "env_target": {
                "ui:description": "目标环境类型",
                "ui:enumNames": ["基准环境", "子环境", "子环境2.0"]
            },
            "senv": {
                "ui:description": "子环境名称",
            },
            "cluster": {
                "ui:description": "集群名称"
            },
            "namespace": {
                "ui:description": "命名空间"
            },
        },
        "formData": {
            "service_id": service_id,
            "service_name": service_name,
            "env": "",
            "env_target": "origin",
            "senv": "",
            "cluster": "",
            "namespace": "",
        },
        "instructions": "请选择需要回滚的环境",
    }

    return json.dumps(form_dict)

def generate_rollback_execution_form(cluster: str, namespace: str, release_list: List[dict]):
    release_ids = [release['id'] for release in release_list]
    release_labels = [f"{release['operatorByChineseName']}/{release['operatorByEmployeeNo']} {release['branch']} {release['description']}" for release in release_list]
    form_dict = {
        "type": "formSchema",
        "schema": {
            "title": "选择服务回滚的版本",
            "type": "object",
            "required": [
                "release_id",
                "description"
            ],
            "properties": {
               "cluster": {
                   "type": "string",
                    "title": "集群名称",
                    "default": cluster
               },
               "namespace": {
                   "type": "string",
                    "title": "命名空间",
                    "default": namespace
               },
                "release_id": {
                    "type": "number",
                    "title": "选择回滚版本",
                    "enum": release_ids
                },
                "description": {
                    "type": "string",
                    "title": "回滚原因"
                }
            }
        },
        "uiSchema": {
            "cluster": {
                "ui:description": "集群名称"
            },
            "namespace": {
                "ui:description": "命名空间"
            },
            "release_id": {
                "ui:description": "选择回滚版本",
                "ui:enumNames": release_labels
            },
            "description": {
                "ui:description": "描述",
                "ui:placeholder": "请输入回滚原因",
                "ui:widget": "textarea"
            }
        },
        "formData": {
            "release_id": "",
            "description": "",
        },
        "instructions": "请选择需要回滚的版本",
    }

    return json.dumps(form_dict)
