import typing
import json
from .agent import CicdAgent

from a2a.server.agent_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.types import (
    TaskArtifactUpdateEvent,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
    DataPart
)
from a2a.utils import new_agent_text_message, new_task, new_text_artifact, new_agent_parts_message


class CicdAgentExecutor(AgentExecutor):
    def __init__(self):
        self.agent = CicdAgent()

    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        query = context.get_user_input()
        
        print("context message:", context.message)
        # 检查是否有表单数据
        message = context.message
        form_data = None
        if message and message.parts:
            for part in message.parts:
                if hasattr(part.root, 'data') and isinstance(part.root, DataPart):
                    form_data = part.root.data
                    break
        
        # 如果有表单数据，构造特殊查询字符串
        if form_data:
            # 提取表单中的关键字段，用于判断表单类型
            form_keys = set(form_data.keys())
            service_id = form_data.get('service_id', '')
            
            # 构造查询字符串
            query_parts = ["用户已提交表单:"]
            
            # 添加表单中的所有字段到查询字符串
            for key, value in form_data.items():
                if value:  # 只添加非空值
                    query_parts.append(f"{key}={value}")
            
            # 添加服务ID特殊标记，用于后续处理
            if service_id:
                query_parts.append(f"用户已选择服务ID={service_id}")
            
            # 添加表单类型提示，帮助模型判断下一步操作
            if 'release_id' in form_keys:
                query_parts.append("操作类型=执行回滚")
            elif 'pipeline_id' in form_keys and 'branch' in form_keys:
                query_parts.append("操作类型=执行流水线")
            elif 'env' in form_keys and 'cluster' in form_keys and 'namespace' in form_keys and 'release_id' not in form_keys:
                # 环境参数表单，用于获取回滚版本列表
                query_parts.append("操作类型=获取回滚版本")
                
                # 处理环境目标类型
                env_target = form_data.get('env_target', 'origin')
                if env_target == 'origin':
                    query_parts.append(f"env_target={form_data['env_target']}")
                elif env_target == 'sub':
                    # 子环境，确保senv已填写
                    if not form_data.get('senv'):
                        return json.dumps({"type": "error", "message": "选择子环境时，必须填写子环境名称"})
                    # env_target保持为sub
                    query_parts.append(f"env_target={env_target}")
            elif 'service_id' in form_keys and len(form_keys) <= 2:  # 只有service_id和可能的service_name
                # 检查是否是回滚操作
                if context.message and any(keyword in context.message.content.lower() for keyword in ["回滚", "rollback"]):
                    query_parts.append("操作类型=准备回滚")
                else:
                    query_parts.append("操作类型=准备流水线")
            
            # 组合最终查询字符串
            query = ", ".join(query_parts)
        
        task = context.current_task

        if not context.message:
            raise Exception('No message provided')

        if not task:
            task = new_task(context.message)
            event_queue.enqueue_event(task)

        # invoke the underlying agent, using streaming results
        print("query:", query)
        async for event in self.agent.stream(query, task.contextId):
            if event['is_task_complete']:
                if event.get('actifact') and event['actifact'] is not None:
                    event_queue.enqueue_event(
                        TaskArtifactUpdateEvent(
                            append=False,
                            contextId=task.contextId,
                            taskId=task.id,
                            lastChunk=True,
                            artifact=event['actifact'],
                        )
                    )
                else:
                    event_queue.enqueue_event(
                        TaskArtifactUpdateEvent(
                            append=False,
                            contextId=task.contextId,
                            taskId=task.id,
                            lastChunk=True,
                            artifact=new_text_artifact(
                                name='当前结果',
                                description='请求CICD Agent执行结果',
                                text=json.dumps(event['content'], ensure_ascii=False) if isinstance(
                                    event['content'], dict) else event['content'],
                            ),
                        )
                    )
                event_queue.enqueue_event(
                    TaskStatusUpdateEvent(
                        status=TaskStatus(state=TaskState.completed),
                        final=True,
                        contextId=task.contextId,
                        taskId=task.id,
                    )
                )
            elif event['require_user_input']:
                if event.get('actifact') and event['actifact'] is not None:
                    event_queue.enqueue_event(
                        TaskArtifactUpdateEvent(
                            append=False,
                            contextId=task.contextId,
                            taskId=task.id,
                            lastChunk=True,
                            artifact=event['actifact'],
                        )
                    )
                else:
                    event_queue.enqueue_event(
                        TaskStatusUpdateEvent(
                            status=TaskStatus(
                                state=TaskState.input_required,
                                message=new_agent_text_message(
                                    event['content'],
                                    task.contextId,
                                    task.id,
                                ),
                            ),
                            final=True,
                            contextId=task.contextId,
                            taskId=task.id,
                        )
                    )
            else:
                if event.get('actifact') and event['actifact'] is not None:
                    event_queue.enqueue_event(
                        TaskArtifactUpdateEvent(
                            append=False,
                            contextId=task.contextId,
                            taskId=task.id,
                            lastChunk=True,
                            artifact=event['actifact'],
                        )
                    )
                event_queue.enqueue_event(
                    TaskStatusUpdateEvent(
                        status=TaskStatus(
                            state=TaskState.working,
                            message=new_agent_text_message(
                                event['content'],
                                task.contextId,
                                task.id,
                            ),
                        ),
                        final=False,
                        contextId=task.contextId,
                        taskId=task.id,
                    )
                )

    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        raise Exception('cancel not supported')
