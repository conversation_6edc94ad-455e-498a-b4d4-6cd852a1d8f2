import logging
import json
import os
from .agent import CicdAgent

from a2a.server.agent_execution import Agent<PERSON><PERSON>cutor, RequestContext
from a2a.server.events import EventQueue
from a2a.server.tasks import TaskUpdater
from a2a.types import (
    InternalError,
    InvalidParamsError,
    Part,
    Task,
    TaskState,
    TextPart,
    UnsupportedOperationError,
    DataPart
)
from a2a.utils import (new_agent_text_message, new_task, new_text_artifact, new_agent_parts_message)

from a2a.utils.errors import ServerError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 从环境变量获取API Key，如果没有设置则使用默认值
EXPECTED_API_KEY = os.getenv("CICD_AGENT_API_KEY")

class CicdAgentExecutor(AgentExecutor):
    def __init__(self):
        self.agent = CicdAgent()

    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        error = self._validate_request(context)
        if error:
            raise ServerError(error=InvalidParamsError())
        
        query = context.get_user_input()
        
        print("context message:", context.message)
        # 检查是否有表单数据
        message = context.message
        form_data = None
        if message and message.parts:
            for part in message.parts:
                if hasattr(part.root, 'data') and isinstance(part.root, DataPart):
                    form_data = part.root.data
                    break
        
        # 如果有表单数据，构造特殊查询字符串
        if form_data:
            opearator_account = context.call_context.state['headers']['operator-account']
            form_data['operatorByEmployeeNo'] = opearator_account
            # 提取表单中的关键字段，用于判断表单类型
            form_keys = set(form_data.keys())
            service_id = form_data.get('service_id', '')
            
            # 构造查询字符串
            query_parts = ["用户已提交表单:"]
            
            # 添加表单中的所有字段到查询字符串
            for key, value in form_data.items():
                if value:  # 只添加非空值
                    query_parts.append(f"{key}={value}")
            
            # 添加服务ID特殊标记，用于后续处理
            if service_id:
                query_parts.append(f"用户已选择服务ID={service_id}")
            
            # 添加表单类型提示，帮助模型判断下一步操作
            if 'release_id' in form_keys:
                query_parts.append("操作类型=执行回滚")
            elif 'pipeline_id' in form_keys and 'branch' in form_keys:
                query_parts.append("操作类型=执行流水线")
            elif 'env' in form_keys and 'cluster' in form_keys and 'namespace' in form_keys and 'release_id' not in form_keys:
                # 环境参数表单，用于获取回滚版本列表
                query_parts.append("操作类型=获取回滚版本")
                
                # 处理环境目标类型
                env_target = form_data.get('env_target', 'origin')
                if env_target == 'origin':
                    query_parts.append(f"env_target={form_data['env_target']}")
                elif env_target == 'sub':
                    # 子环境，确保senv已填写
                    if not form_data.get('senv'):
                        return json.dumps({"type": "error", "message": "选择子环境时，必须填写子环境名称"})
                    # env_target保持为sub
                    query_parts.append(f"env_target={env_target}")
            elif 'service_id' in form_keys and len(form_keys) <= 2:  # 只有service_id和可能的service_name
                # 检查是否是回滚操作
                if context.message and any(keyword in context.message.content.lower() for keyword in ["回滚", "rollback"]):
                    query_parts.append("操作类型=准备回滚")
                else:
                    query_parts.append("操作类型=准备流水线")
            
            # 组合最终查询字符串
            query = ", ".join(query_parts)
        
        task = context.current_task
        if not task:
            task = new_task(context.message)
            await event_queue.enqueue_event(task)

        # invoke the underlying agent, using streaming results
        print("query:", query)
        updater = TaskUpdater(event_queue, task.id, task.contextId)
        try:
            async for item in self.agent.stream(query, task.contextId):
                is_task_complete = item['is_task_complete']
                require_user_input = item['require_user_input']

                if not is_task_complete and not require_user_input:
                    await updater.update_status(
                        TaskState.working,
                        new_agent_text_message(
                            item['content'],
                            task.contextId,
                            task.id,
                        ),
                    )
                elif require_user_input:
                    if item.get("formSchema", {}).get("type") == "formSchema":
                        parts = [DataPart(
                            kind="data",
                            data=item["formSchema"]
                        )]
                        await updater.update_status(
                            TaskState.input_required,
                            new_agent_parts_message(
                                parts,
                                task.contextId,
                                task.id,
                            ),
                            final=True,
                        )
                    else:
                        await updater.update_status(
                            TaskState.input_required,
                            new_agent_text_message(
                                item['content'],
                                task.contextId,
                                task.id,
                            ),
                            final=True,
                        )
                    break
                else:
                    await updater.add_artifact(
                        [Part(root=TextPart(text=item['content']))],
                        name='当前结果',
                    )
                    await updater.complete()
                    break
        except Exception as e:
            logger.error(f'An error occurred while streaming the response: {e}')
            raise ServerError(error=InternalError()) from e
    
    def _validate_request(self, context: RequestContext) -> bool:
        """验证请求，包括API Key验证"""
        # 获取请求头中的API Key
        headers = context.call_context.state['headers']
        api_key = headers.get('x-api-key', None)
        # 验证API Key
        if EXPECTED_API_KEY and api_key != EXPECTED_API_KEY:
            logger.warning(f"Invalid API key provided: {api_key}")
            return True

        employeeNo = headers.get('operator-account', None)
        if not employeeNo:
            logger.warning(f"Operator-Account. not provided")
            return True

        return False  # 返回False表示验证通过

    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        raise ServerError(error=UnsupportedOperationError())
