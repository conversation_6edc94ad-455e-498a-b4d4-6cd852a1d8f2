# CICD 知识库

这个目录包含CICD系统的用户手册和问题处理文档，用于为CICD代理提供知识支持。

## 目录结构

- `text/`: 存放纯文本格式的文档 (.txt)
- `pdf/`: 存放PDF格式的文档 (.pdf)
- `word/`: 存放Word格式的文档 (.docx)
- `vector_db/`: 存放向量数据库文件（自动生成，请勿手动修改）

## 如何添加文档

1. 将文档放入对应的目录中：
   - 文本文件 (.txt) 放入 `text/` 目录
   - PDF文件 (.pdf) 放入 `pdf/` 目录
   - Word文件 (.docx) 放入 `word/` 目录

2. 重建知识库：
   - 启动CICD代理后，可以通过发送消息"重建知识库"来触发知识库重建
   - 或者在代码中调用 `rebuild_knowledge_base()` 函数

## 文档格式建议

为了获得最佳的检索效果，建议文档遵循以下格式：

1. 使用清晰的标题和小标题
2. 将相关内容组织在一起
3. 使用简洁明了的语言
4. 对于常见问题，使用问答格式

## 示例文档

可以在各个目录中添加以下类型的文档：

1. CICD系统用户手册
2. 常见问题解答 (FAQ)
3. 故障排除指南
4. 最佳实践文档
5. 系统架构说明