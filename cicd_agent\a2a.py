import httpx

from .agent import CicdAgent
from .agent_executor import CicdAgentExecutor

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryPushNotifier, InMemoryTaskStore
from a2a.types import AgentCapabilities, AgentCard, AgentSkill, APIKeySecurityScheme, In

def get_cicd_server(host: str, port: int) -> A2AStarletteApplication:
    client = httpx.AsyncClient()
    request_handler = DefaultRequestHandler(
        agent_executor=CicdAgentExecutor(),
        task_store=InMemoryTaskStore(),
        push_notifier=InMemoryPushNotifier(client),
    )

    server = A2AStarletteApplication(
        agent_card=get_agent_card(host, port), http_handler=request_handler
    )
    return server

def get_agent_card(host: str, port: int) -> AgentCard:
    """Returns the Agent Card for the Cicd Agent."""
    capabilities = AgentCapabilities(streaming=True, pushNotifications=True)
    skill = AgentSkill(
        id='cicd_assistant',
        name='CICD使用助手',
        description='支持CICD流水线的查询、执行和回滚，以及知识库查询功能',
        tags=['流水线执行', '版本回滚', '服务查询', '知识库查询'],
        examples=[
            '查询wc服务信息',
            '执行wc服务的流水线',
            '回滚wc到上一个版本',
            '如何创建流水线？',
            '查询知识库中关于回滚的内容'
        ],
    )
    return AgentCard(
        name='CICD使用助手',
        description='服务查询、流水线运行、服务回滚、知识库查询',
        url=f'http://{host}:{port}/',
        version='1.0.0',
        defaultInputModes=CicdAgent.SUPPORTED_CONTENT_TYPES,
        defaultOutputModes=CicdAgent.SUPPORTED_CONTENT_TYPES,
        capabilities=capabilities,
        skills=[skill],
        securitySchemes={
            "api_key": APIKeySecurityScheme(
                description="API key 鉴权",
                name="X-API-KEY",
                **{"in": In.header}  # 使用字典解包，因为in是Python关键字
            )
        },
        security=[
            {
                'api_key': ["123456"]
            }
        ],
    )
