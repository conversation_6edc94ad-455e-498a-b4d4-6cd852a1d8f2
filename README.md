# CICD 代理系统

这是一个基于大语言模型的CICD运维助手，可以帮助用户查询服务信息、执行流水线、回滚操作，以及提供知识库查询功能。

## 功能特点

- 服务查询：查询和管理服务信息
- 流水线执行：选择分支和流水线，执行构建和部署
- 版本回滚：在部署出现问题时，快速回滚到之前的稳定版本
- 知识库查询：提供CICD系统使用方法、常见问题和故障排除指南

## 安装和运行

1. 安装依赖：

```bash
pip install -r requirements.txt
```

2. 设置环境变量：

```bash
export OPENAI_API_KEY=your_openai_api_key
export OPENAI_API_BASE=your_openai_api_base
export CICD_API_TOKEN=your_cicd_api_token
export CICD_API_BASE_URL=your_cicd_api_base_url
```

3. 初始化知识库：

```bash
python scripts/create_knowledge_files.py
```

4. 运行服务：

```bash
python main.py
```

## 知识库管理

### 添加文档

1. 将文档放入对应的目录中：
   - 文本文件 (.txt) 放入 `knowledge/text/` 目录
   - PDF文件 (.pdf) 放入 `knowledge/pdf/` 目录
   - Word文件 (.docx) 放入 `knowledge/word/` 目录

2. 知识库更新：
   - 系统会自动检测新增或修改的文档，并更新知识库
   - 如需手动更新，可以发送消息"更新知识库"

### 查询知识库

用户可以通过以下方式查询知识库：

1. 直接提问：例如"如何执行流水线？"、"回滚操作有哪些注意事项？"
2. 列出文档：发送消息"列出知识库文档"查看所有可用文档
3. 特定查询：例如"查询关于回滚的内容"

## 使用示例

1. 查询服务：
   ```
   查询服务名为user-service的信息
   ```

2. 执行流水线：
   ```
   执行user-service的流水线
   ```

3. 回滚操作：
   ```
   回滚user-service到之前的版本
   ```

4. 知识库查询：
   ```
   如何执行流水线？
   ```


