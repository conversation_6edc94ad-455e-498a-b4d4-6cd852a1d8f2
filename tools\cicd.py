import requests
import json
import os
from dotenv import load_dotenv

from langchain_core.tools import tool
from typing import Any, List

# 加载环境变量
load_dotenv()

exist_key = [
    "id",
    "name",
    "buildPath",
    "langName",
    "langVersion",
    "repoAddr",
    "projectID",
    "description",
    "projectName"
]

# 从环境变量获取配置
HEADERS = {
    "Content-Type": "application/json",
    "X-Token": os.getenv("CICD_API_TOKEN", "Y2ljZA==.d0d2b3a430bb92dac787dfcb0afca01e26909a4d33e2820d1fb030dda0b051f4")
}

BASE_URL = os.getenv("CICD_API_BASE_URL", "http://cicd-testing.ttyuyin.com/b/api/v1/")

@tool
def cicd_search(query: str) -> List[Any] | str:
    """
    use this to search cicd resource detail.
    Args:
        query: app name (e.g., wc).

    Returns:
        A dictionary containing app detail, or an error message if the request fails
    """
    try:
        response = requests.get(
                f"{BASE_URL}app/apps/find?appName={query}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        apps = result.get("data", [])
        if len(apps) == 1:
            app_result = {}
            for key, value in apps[0].items():
                if key in exist_key:
                    app_result[key] = value
            return app_result
        elif len(apps) > 10:
            return f"""
            查询到超过10个服务，请尝试减少查询条件。
            """
        elif len(apps) > 1:
            app_results = []
            for item in apps:
                app_result = {}
                for key, value in item.items():
                    if key in exist_key:
                        app_result[key] = value
                app_results.append(app_result)
            return f"""查询到多个服务：
            {app_results}
            """
        else:
            return "查询不到服务信息，请确认服务是否存在"
    except Exception as e:
        return f"查询失败: {str(e)}"
    
def generate_select_service_form(services: List[dict]):
    service_ids = [service.get('id', '') for service in services]
    service_names = [f'{service.get('name', '')-service.get('projectName', '')}' for service in services]
    form_dict = {
        "type": "formSchema",
        "schema": {
            "title": "选择服务",
            "type": "object",
            "required": [
                "service_id"
            ],
            "properties": {
                "service_id": {
                    "type": "number",
                    "title": "服务选择",
                    "enum": service_ids
                }
            },
            "uiSchema": {
                "service_id": {
                    "ui:description": "服务选择",
                    "ui:enumNames": service_names
                }
            }
        },
        "form_data": {
            "service_id": ""
        },
        "instructions": "请选择一个服务执行操作"
    }
   
    return json.dumps(form_dict)