import requests
import json
import os
from dotenv import load_dotenv

from langchain_core.tools import tool
from typing import Any, List
from a2a.types import DataPart

# 加载环境变量
load_dotenv()

exist_key = [
    "id",
    "name",
    "buildPath",
    "langName",
    "langVersion",
    "repoAddr",
    "projectID",
    "description",
    "projectName"
]

# 从环境变量获取配置
HEADERS = {
    "Content-Type": "application/json",
    "X-Token": os.getenv("CICD_API_TOKEN", "Y2ljZA==.d0d2b3a430bb92dac787dfcb0afca01e26909a4d33e2820d1fb030dda0b051f4")
}

BASE_URL = os.getenv("CICD_API_BASE_URL", "http://cicd-testing.ttyuyin.com/b/api/v1/")

@tool
def cicd_search(query: str) -> List[Any] | str:
    """
    use this to search cicd resource detail.
    Args:
        query: app name (e.g., wc).

    Returns:
        A dictionary containing app detail, or an error message if the request fails
    """
    try:
        response = requests.get(
                f"{BASE_URL}app/apps/find?appName={query}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        apps = result.get("data", [])
        if len(apps) == 1:
            app_result = {}
            for key, value in apps[0].items():
                if key in exist_key:
                    app_result[key] = value
            return app_result
        elif len(apps) > 10:
            return f"""
            查询到超过10个服务，请尝试减少查询条件。
            """
        elif len(apps) > 1:
            app_results = []
            for item in apps:
                app_result = {}
                for key, value in item.items():
                    if key in exist_key:
                        app_result[key] = value
                app_results.append(app_result)
            return f"""查询到多个服务：
            {app_results}
            """
        else:
            return "查询不到服务信息，请确认服务是否存在"
    except Exception as e:
        return f"查询失败: {str(e)}"
@tool
def get_rollback_list(app_id: str, env: str, env_target: str, namespace: str, cluster: str, senv: str = "") -> str:
    """
    获取回滚列表
    Args:
        app_id: 应用ID
        env: 环境（dev/testing/staging/production)
        env_target: 目标环境类型（origin/sub/sub_v2）
        namespace: 命名空间
        cluster: 集群
        senv: 子环境名称，当env_target为sub_v2时必填
    Returns:
        回滚列表表单
    """ 
    try:
        # 构建API请求参数
        params = {
            "appId": app_id,
            "env": env,
            "namespace": namespace,
            "cluster": cluster,
            "envTarget": env_target,
        }
        
        # 子环境2.0
        if env_target == 'sub_v2':
            if not senv:
                return json.dumps({"type": "error", "message": "选择子环境时，必须提供子环境名称"})
            params["senv"] = senv
        
        # 构建URL查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        
        response = requests.get(
            f"{BASE_URL}deploy/runtime/rollback-list?{query_string}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        data = result.get("data", [])
        
        if not data or isinstance(data, str):
            return json.dumps({"type": "error", "message": f"获取回滚列表失败或无可回滚版本: {data if isinstance(data, str) else '无可回滚版本'}"})
        
        # 创建回滚版本选择表单
        return json.dumps({
            'type': 'form',
            'form': {
                'type': 'object',
                'properties': {
                    'service_id': {'type': 'string', 'title': '服务ID', 'readOnly': True},
                    'env': {'type': 'string', 'title': '环境', 'readOnly': True},
                    'env_target': {'type': 'string', 'title': '目标环境类型', 'readOnly': True},
                    'senv': {'type': 'string', 'title': '子环境名称', 'readOnly': True},
                    'cluster': {'type': 'string', 'title': '集群', 'readOnly': True},
                    'namespace': {'type': 'string', 'title': '命名空间', 'readOnly': True},
                    'release_id': {
                        'type': 'string',
                        'enum': [{'value': str(r.get('id', '')), 'label': f"{r.get('id', '')} {r.get('operatorByChineseName', '')}-{r.get('operatorByEmployeeNo', '')} {r.get('branch', '')} \n {r.get('description', '')}"} for r in data],
                        'title': '选择回滚版本',
                    },
                },
                'required': ['release_id'],
            },
            'form_data': {
                'service_id': str(app_id),
                'env': env,
                'env_target': env_target,
                'senv': senv,
                'cluster': cluster,
                'namespace': namespace,
                'release_id': '',
            },
            'instructions': '请选择要回滚到的版本',
        })
    except Exception as e:
        return json.dumps({"type": "error", "message": f"获取回滚列表失败: {str(e)}"})

@tool
def rollback(release_id: str) -> str:
    """
    回滚
    Args:
        release_id: 发布ID
    Returns:
        回滚结果
    """
    operatorByEmployeeNo = "T2517"
    try:
        response = requests.post(
            f"{BASE_URL}deploy/runtime/openapi/rollback",
            json={
                "releaseId": int(release_id),
                "description": "agent回滚",
                "operatorByEmployeeNo": operatorByEmployeeNo,
            },
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        return result
    except Exception as e:
        return f"执行回滚失败: {str(e)}"

@tool
def prepare_rollback_execution(service_name: str) -> str:
    """
    准备回滚执行，获取服务信息、环境参数和回滚版本列表
    Args:
        service_name: 服务名称或包含"用户已选择服务ID="的消息
    Returns:
        包含服务信息、环境选择和回滚版本的JSON格式表单，供用户选择
    """
    try:
        # 提取服务ID或使用服务名称
        service_id = None
        if "用户已选择服务ID=" in service_name:
            service_id = service_name.split("用户已选择服务ID=")[1].strip()
            # 直接获取服务信息
            response = requests.get(
                f"{BASE_URL}app/apps/{service_id}",
                headers=HEADERS,
                timeout=10,
            )
            result = response.json()
            service_info = result.get("data", {})
            if not service_info:
                return json.dumps({"type": "error", "message": f"无法获取服务ID为{service_id}的信息"})
        else:
            # 查询服务信息
            service_info = cicd_search(service_name)
            
            # 处理多个服务的情况
            if isinstance(service_info, str) and "查询到多个服务" in service_info:
                # 提取服务列表并创建选择表单
                import re
                import ast
                match = re.search(r'\[(.*)\]', service_info)
                if match:
                    services_str = "[" + match.group(1) + "]"
                    services_list = ast.literal_eval(services_str)
                    
                    # 简化的服务选择表单
                    return json.dumps({
                        'type': 'form',
                        'form': {
                            'type': 'object',
                            'properties': {
                                'service_id': {
                                    'type': 'string',
                                    'enum': [{'value': str(s.get('id', '')), 'label': f"{s.get('name', '')} - {s.get('projectName', '')}"} for s in services_list],
                                    'title': '服务选择',
                                },
                            },
                            'required': ['service_id'],
                        },
                        'form_data': {'service_id': ''},
                        'instructions': f'请选择一个服务以进行回滚操作',
                    })
            
            # 处理错误情况
            if isinstance(service_info, str):
                return json.dumps({"type": "error", "message": service_info})
            
            service_id = service_info.get('id')
            
        # 创建环境选择表单
        return json.dumps({
            'type': 'form',
            'form': {
                'type': 'object',
                'properties': {
                    'service_id': {'type': 'string', 'title': '服务ID', 'readOnly': True},
                    'service_name': {'type': 'string', 'title': '服务名称', 'readOnly': True},
                    'env': {
                        'type': 'string',
                        'enum': [
                            {'value': 'dev', 'label': '开发环境'},
                            {'value': 'testing', 'label': '测试环境'},
                            {'value': 'staging', 'label': '预发布环境'},
                            {'value': 'production', 'label': '正式环境'}
                        ],
                        'title': '环境选择',
                    },
                    'env_target': {
                        'type': 'string',
                        'enum': [
                            {'value': 'origin', 'label': '基准环境'},
                            {'value': 'sub', 'label': '子环境1.0'},
                            {'value': 'sub_v2', 'label': '子环境2.0'}
                        ],
                        'title': '目标环境类型',
                    },
                    'senv': {
                        'type': 'string', 
                        'title': '子环境名称', 
                        'description': '当选择子环境时必填'
                    },
                    'cluster': {'type': 'string', 'title': '集群名称'},
                    'namespace': {'type': 'string', 'title': '命名空间'},
                },
                'required': ['env', 'env_target', 'cluster', 'namespace'],
                'dependencies': {
                    'env_target': {
                        'oneOf': [
                            {
                                'properties': {
                                    'env_target': {'enum': ['origin']}
                                }
                            },
                            {
                                'properties': {
                                    'env_target': {'enum': ['sub']}
                                }
                            },
                            {
                                'properties': {
                                    'env_target': {'enum': ['sub_v2']}
                                },
                                'required': ['senv']
                            }
                        ]
                    }
                }
            },
            'form_data': {
                'service_id': str(service_id),
                'service_name': service_info.get('name', service_name),
                'env': '',
                'env_target': 'origin',
                'senv': '',
                'cluster': 'default',
                'namespace': 'default',
            },
            'instructions': '请选择要回滚的环境参数。如果选择子环境，请填写子环境名称。',
        })

    except Exception as e:
        return json.dumps({"type": "error", "message": f"准备回滚操作失败: {str(e)}"})
