import os
import json
from typing import Any
from langchain_core.tools import tool
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    DirectoryLoader,
    PyPDFLoader,
    Docx2txtLoader
)
from langchain_community.document_loaders.text import TextLoader

# 知识库路径
KNOWLEDGE_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "knowledge")
# 向量数据库路径
VECTOR_DB_PATH = os.path.join(KNOWLEDGE_DIR, "vector_db")

# 初始化嵌入模型
embeddings = OpenAIEmbeddings()

# 自定义TextLoader，强制使用UTF-8编码
class UTF8TextLoader(TextLoader):
    """加载文本文件，强制使用UTF-8编码"""
    
    def __init__(self, file_path):
        super().__init__(file_path, encoding="utf-8", autodetect_encoding=True)

# 检查向量数据库是否存在，如果不存在则创建
def initialize_vector_db():
    """初始化向量数据库，如果不存在则从文档创建"""
    if os.path.exists(VECTOR_DB_PATH) and os.path.isdir(VECTOR_DB_PATH):
        try:
            # 尝试加载现有的向量数据库
            vector_db = FAISS.load_local(VECTOR_DB_PATH, embeddings)
            print("成功加载现有向量数据库")
            return vector_db
        except Exception as e:
            print(f"加载向量数据库失败: {e}，将重新创建")
    
    # 创建知识库目录（如果不存在）
    os.makedirs(KNOWLEDGE_DIR, exist_ok=True)
    
    # 加载文档
    documents = []
    
    # 加载文本文件
    if os.path.exists(os.path.join(KNOWLEDGE_DIR, "text")):
        text_loader = DirectoryLoader(
            os.path.join(KNOWLEDGE_DIR, "text"),
            glob="**/*.txt",
            loader_cls=UTF8TextLoader
        )
        documents.extend(text_loader.load())
    
    # 加载PDF文件
    if os.path.exists(os.path.join(KNOWLEDGE_DIR, "pdf")):
        pdf_loader = DirectoryLoader(
            os.path.join(KNOWLEDGE_DIR, "pdf"),
            glob="**/*.pdf",
            loader_cls=PyPDFLoader
        )
        documents.extend(pdf_loader.load())
    
    # 加载Word文件
    if os.path.exists(os.path.join(KNOWLEDGE_DIR, "word")):
        docx_loader = DirectoryLoader(
            os.path.join(KNOWLEDGE_DIR, "word"),
            glob="**/*.docx",
            loader_cls=Docx2txtLoader
        )
        documents.extend(docx_loader.load())
    
    if not documents:
        print("警告: 未找到任何文档，知识库将为空")
        # 创建一个空的向量存储
        vector_db = FAISS.from_texts(["空知识库占位符"], embeddings)
        vector_db.save_local(VECTOR_DB_PATH)
        return vector_db
    
    # 分割文档
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?", " ", ""]
    )
    chunks = text_splitter.split_documents(documents)
    
    # 创建向量数据库
    vector_db = FAISS.from_documents(chunks, embeddings)
    
    # 保存向量数据库
    os.makedirs(VECTOR_DB_PATH, exist_ok=True)
    vector_db.save_local(VECTOR_DB_PATH)
    print(f"成功创建向量数据库，共包含 {len(chunks)} 个文档块")
    
    return vector_db

# 初始化向量数据库
vector_db = initialize_vector_db()

@tool
def search_knowledge_base(query: str) -> str:
    """
    搜索知识库，查找与查询相关的信息
    Args:
        query: 用户的查询问题
    Returns:
        与查询相关的知识库内容
    """
    try:
        # 搜索向量数据库
        docs = vector_db.similarity_search(query, k=3)
        
        if not docs:
            return "未找到相关信息，请尝试使用其他关键词或更具体的问题。"
        
        # 格式化结果
        results = []
        for i, doc in enumerate(docs, 1):
            source = doc.metadata.get("source", "未知来源")
            # 提取文件名
            filename = os.path.basename(source) if source else "未知文件"
            # 格式化内容
            content = doc.page_content.strip()
            results.append(f"文档 {i} ({filename}):\n{content}\n")
        
        return "\n".join(results)
    except Exception as e:
        return f"搜索知识库时出错: {str(e)}"

@tool
def list_knowledge_documents() -> str:
    """
    列出知识库中的所有文档
    Returns:
        知识库中的文档列表
    """
    try:
        documents = []
        
        # 检查并列出文本文件
        text_dir = os.path.join(KNOWLEDGE_DIR, "text")
        if os.path.exists(text_dir):
            for root, _, files in os.walk(text_dir):
                for file in files:
                    if file.endswith(".txt"):
                        rel_path = os.path.relpath(os.path.join(root, file), KNOWLEDGE_DIR)
                        documents.append(rel_path)
        
        # 检查并列出PDF文件
        pdf_dir = os.path.join(KNOWLEDGE_DIR, "pdf")
        if os.path.exists(pdf_dir):
            for root, _, files in os.walk(pdf_dir):
                for file in files:
                    if file.endswith(".pdf"):
                        rel_path = os.path.relpath(os.path.join(root, file), KNOWLEDGE_DIR)
                        documents.append(rel_path)
        
        # 检查并列出Word文件
        word_dir = os.path.join(KNOWLEDGE_DIR, "word")
        if os.path.exists(word_dir):
            for root, _, files in os.walk(word_dir):
                for file in files:
                    if file.endswith(".docx"):
                        rel_path = os.path.relpath(os.path.join(root, file), KNOWLEDGE_DIR)
                        documents.append(rel_path)
        
        if not documents:
            return "知识库中没有文档。请先添加文档到知识库。"
        
        # 按文档类型分类
        result = "知识库文档列表:\n\n"
        
        # 文本文件
        txt_docs = [doc for doc in documents if doc.endswith(".txt")]
        if txt_docs:
            result += "文本文件:\n" + "\n".join(f"- {doc}" for doc in txt_docs) + "\n\n"
        
        # PDF文件
        pdf_docs = [doc for doc in documents if doc.endswith(".pdf")]
        if pdf_docs:
            result += "PDF文件:\n" + "\n".join(f"- {doc}" for doc in pdf_docs) + "\n\n"
        
        # Word文件
        docx_docs = [doc for doc in documents if doc.endswith(".docx")]
        if docx_docs:
            result += "Word文件:\n" + "\n".join(f"- {doc}" for doc in docx_docs)
        
        return result
    except Exception as e:
        return f"列出知识库文档时出错: {str(e)}"

@tool
def rebuild_knowledge_base() -> str:
    """
    重建知识库向量数据库
    Returns:
        重建结果
    """
    try:
        global vector_db
        # 删除现有向量数据库
        if os.path.exists(VECTOR_DB_PATH):
            import shutil
            shutil.rmtree(VECTOR_DB_PATH)
        
        # 重新初始化向量数据库
        vector_db = initialize_vector_db()
        
        return "知识库已成功重建。"
    except Exception as e:
        return f"重建知识库时出错: {str(e)}"




