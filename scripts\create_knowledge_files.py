import os
import sys

def create_directory_structure():
    """创建知识库目录结构"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    knowledge_dir = os.path.join(base_dir, "knowledge")
    
    # 创建主目录
    os.makedirs(knowledge_dir, exist_ok=True)
    
    # 创建子目录
    subdirs = ["text", "pdf", "word", "vector_db"]
    for subdir in subdirs:
        os.makedirs(os.path.join(knowledge_dir, subdir), exist_ok=True)
    
    print(f"已创建知识库目录结构: {knowledge_dir}")

def create_sample_files():
    """创建示例文件"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 创建FAQ文件
    faq_content = """# CICD系统常见问题解答

## 基本概念

### 什么是CICD系统？
CICD系统是持续集成/持续部署系统的简称，用于自动化软件的构建、测试和部署过程。我们的CICD系统提供了一站式的服务管理、流水线执行和版本回滚功能。

### CICD系统的主要功能有哪些？
1. 服务管理：查询和管理服务信息
2. 流水线执行：选择分支和流水线，执行构建和部署
3. 版本回滚：在部署出现问题时，快速回滚到之前的稳定版本
4. 环境管理：支持多环境部署和管理

## 流水线执行

### 如何执行流水线？
1. 查询服务：输入服务名称查询
2. 选择服务：从查询结果中选择目标服务
3. 选择分支和流水线：选择要构建的代码分支和执行的流水线
4. 执行流水线：系统会自动执行选定的流水线
"""
    
    # 创建用户指南文件
    guide_content = """# CICD系统用户指南

## 系统概述

CICD系统是一个集成了持续集成和持续部署功能的平台，旨在帮助开发团队自动化软件的构建、测试和部署过程。本指南将帮助您了解如何使用CICD系统的各项功能。

## 系统访问

### 访问方式
1. Web界面：通过浏览器访问 http://cicd-testing.ttyuyin.com
2. API接口：通过API调用访问系统功能
3. 聊天机器人：通过聊天界面与CICD助手交互
"""
    
    # 保存文件，确保使用UTF-8编码
    faq_path = os.path.join(base_dir, "knowledge", "text", "cicd_faq.txt")
    with open(faq_path, "w", encoding="utf-8") as f:
        f.write(faq_content)
    
    guide_path = os.path.join(base_dir, "knowledge", "text", "cicd_user_guide.txt")
    with open(guide_path, "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print(f"已创建示例文件:")
    print(f"- {faq_path}")
    print(f"- {guide_path}")

def main():
    create_directory_structure()
    create_sample_files()
    print("知识库初始化完成。请运行 'python -m cicd_agent.agent' 启动代理，然后发送 '重建知识库' 消息来构建向量数据库。")

if __name__ == "__main__":
    main()