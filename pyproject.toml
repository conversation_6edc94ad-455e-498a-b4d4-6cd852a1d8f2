[project]
name = "a2a-cicd"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "a2a-sdk>=0.2.10",
    "click>=8.2.1",
    "docx2txt>=0.9",
    "dotenv>=0.9.9",
    "faiss-cpu>=1.11.0",
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "langchain-core>=0.3.62",
    "langchain-openai>=0.3.18",
    "langchain-text-splitters>=0.3.8",
    "langfuse>=2.60.7",
    "langgraph>=0.4.7",
    "pypdf>=5.6.0",
    "uvicorn>=0.34.2",
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

