import os
import sys

import click

from cicd_agent import get_cicd_server

from dotenv import load_dotenv

load_dotenv()

def main():
    host = os.getenv('HOST', 'localhost')
    port = int(os.getenv('PORT', 8080))
    if not os.getenv('OPENAI_API_KEY'):
        print('OPENAI_API_KEY environment variable not set.')
        sys.exit(1)
    
    server = get_cicd_server(host, port)
    app = server.build()
    from starlette.middleware.cors import CORSMiddleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=port)


if __name__ == '__main__':
    main()
