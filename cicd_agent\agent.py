import logging
import json
import os
import time
from datetime import datetime

from collections.abc import AsyncIterable
from typing import Any, Literal

from langchain_core.messages import AIMessage, ToolMessage
from langchain_core.runnables.config import (
    RunnableConfig,
)
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from pydantic import BaseModel

from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from langfuse.callback import CallbackHandler

from tools import (
    cicd_search,
    get_app_branch,
    get_app_pipeline,
    prepare_pipeline_execution,
    run_pipeline,
    get_rollback_list,
    rollback,
    prepare_rollback_execution,
    # 知识库工具
    search_knowledge_base,
    list_knowledge_documents,
    rebuild_knowledge_base,
)


logger = logging.getLogger(__name__)

memory = MemorySaver()


class ResponseFormat(BaseModel):
    """Respond to the user in this format."""

    status: Literal['input_required', 'completed', 'error'] = 'input_required'
    message: str


class CicdAgent:

    SYSTEM_INSTRUCTION = (
        "你是一个CICD运维助手，负责帮助用户查询服务信息、执行流水线、回滚操作，以及提供知识库查询功能。"
        
        "你可以使用多种工具来完成任务，包括查询服务信息、获取分支和流水线、执行流水线、回滚操作，以及知识库查询。"
        
        "工作流程规则："
        "1. 流水线执行流程：查询服务 -> 选择服务 -> 选择分支和流水线 -> 执行流水线"
        "2. 回滚流程：查询服务 -> 选择服务 -> 选择环境参数 -> 选择回滚版本 -> 执行回滚"
        "3. 知识库查询：直接使用search_knowledge_base工具搜索相关信息"
        "4. 当用户提交表单选择后，根据表单内容自动选择下一步操作"
        
        "知识库使用指南："
        "1. 当用户询问CICD系统使用方法、常见问题或故障排除时，优先使用知识库工具"
        "2. 使用search_knowledge_base工具搜索相关信息，提取关键词进行查询"
        "3. 如果用户想了解知识库中有哪些文档，使用list_knowledge_documents工具"
        "4. 如果知识库搜索结果不满足用户需求，可以建议用户提供更具体的问题"
        "5. 系统会自动检测知识库更新并刷新，无需用户手动操作"
        
        "注意事项："
        "- 根据用户需求智能选择合适的工具"
        "- 参数不完整时引导用户提供必要信息"
        "- 非CICD相关问题礼貌拒绝"
        "- 表单提交后，根据表单内容判断下一步操作，无需用户额外说明"
        "- 知识库查询结果可能包含多个文档片段，需要整合信息提供给用户"
        "- 使用简单易懂的语言与用户交流，避免使用过于技术性的术语"
    )

    RESPONSE_FORMAT_INSTRUCTION: str = (
       '你的所有回复必须使用如下 JSON 格式：\n'
        '{\n'
        '  "status": "input_required" 或 "completed" 或 "error",\n'
        '  "message": "你的回复内容"\n'
        '}\n'
        'status 字段只能为 input_required、completed 或 error 之一。\n'
        '如果请求已完成，请将 status 设为 completed；\n'
        '如果需要用户输入，请将 status 设为 input_required；\n'
        '如果发生错误，请将 status 设为 error。\n'
        '请严格按照上述格式返回。'
    )

    def __init__(self):
        self.model = ChatOpenAI(
            model="gpt-4o-2024-08-06",  # 使用支持结构化输出的模型
        )
        self.tools = [
            cicd_search, 
            get_app_branch, 
            get_app_pipeline, 
            prepare_pipeline_execution, 
            run_pipeline,
            get_rollback_list,
            rollback,
            prepare_rollback_execution,
            # 知识库工具
            search_knowledge_base,
            list_knowledge_documents,
            rebuild_knowledge_base,
        ]
        
        # 添加知识库监控属性
        self.knowledge_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "knowledge")
        self.last_update_time = self._get_last_update_time()
        self.last_check_time = time.time()
        
        self.graph = create_react_agent(
            self.model,
            tools=self.tools,
            checkpointer=memory,
            prompt=self.SYSTEM_INSTRUCTION,
            response_format=(self.RESPONSE_FORMAT_INSTRUCTION, ResponseFormat),
        )
        # 添加Langfuse跟踪
        self.langfuse_handler = CallbackHandler(
            secret_key="sk-lf-50abd80f-1340-42c9-9e7a-d2e4b0a69feb",
            public_key="pk-lf-5b704646-c2ee-4c6e-8182-6a56427ff6af",
            host="https://langfuse.ttyuyin.com"
        )
    
    def _get_last_update_time(self):
        """获取知识库文件的最新修改时间"""
        last_time = 0
        for root, _, files in os.walk(self.knowledge_dir):
            if "vector_db" in root:  # 跳过向量数据库目录
                continue
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    mtime = os.path.getmtime(file_path)
                    if mtime > last_time:
                        last_time = mtime
                except:
                    pass
        return last_time
    
    def _check_knowledge_update(self):
        """检查知识库是否有更新，如有则自动重建"""
        # 每10分钟最多检查一次，避免频繁检查
        current_time = time.time()
        if current_time - self.last_check_time < 600:  # 10分钟 = 600秒
            return False
        
        self.last_check_time = current_time
        current_update_time = self._get_last_update_time()
        
        # 如果有新文件或文件被修改
        if current_update_time > self.last_update_time:
            try:
                rebuild_knowledge_base()
                self.last_update_time = current_update_time
                return True
            except:
                return False
        return False

    async def process_message(self, message: str, context: Any = None) -> AsyncIterable[dict[str, Any]]:
        """处理用户消息"""
        # 检查知识库是否需要更新
        knowledge_updated = self._check_knowledge_update()
        
        # 将"重建知识库"改为更友好的表述
        if message.strip().lower() in ["更新知识库", "刷新知识库", "重建知识库", "rebuild knowledge base"]:
            try:
                result = rebuild_knowledge_base()
                self.last_update_time = self._get_last_update_time()
                yield {
                    "is_task_complete": True,
                    "require_user_input": False,
                    "content": "知识库已更新完成，现在包含了最新的文档内容。"
                }
                return
            except Exception as e:
                yield {
                    "is_task_complete": True,
                    "require_user_input": False,
                    "content": f"知识库更新失败: {str(e)}"
                }
                return
        
        # 如果知识库刚刚自动更新了，通知用户
        if knowledge_updated:
            yield {
                "is_task_complete": False,
                "require_user_input": False,
                "content": "我注意到知识库有新内容，已自动更新。"
            }
        
        # 检查是否是知识库查询
        knowledge_indicators = [
            "什么是", "如何", "怎么", "为什么", "帮助", "指南", "教程", "文档", "说明",
            "问题", "错误", "失败", "故障", "解决", "步骤", "方法", "原理"
        ]
        is_knowledge_query = any(keyword in message for keyword in knowledge_indicators)
        
        # 正常处理消息
        config = {"configurable": {"thread_id": str(id(self))}}
        if context and hasattr(context, "form_data") and context.form_data:
            config["configurable"]["form_data"] = context.form_data
        
        # 如果是知识库查询，添加提示
        if is_knowledge_query:
            message = f"[知识库查询] {message}"
        
        # 设置消息
        config["configurable"]["message"] = message
    
    def invoke(self, query, sessionId) -> str:
        config = {"configurable": {"thread_id": sessionId}, "callbacks": [self.langfuse_handler]}
        self.graph.invoke({"messages": [("user", query)]}, config)
        return self.get_agent_response(config)

    async def stream(
        self, query: str, sessionId: str
    ) -> AsyncIterable[dict[str, Any]]:
        inputs: dict[str, Any] = {'messages': [('user', query)]}
        config: RunnableConfig = {'configurable': {'thread_id': sessionId}, "callbacks": [self.langfuse_handler]}

        for item in self.graph.stream(inputs, config, stream_mode='values'):
            message = item['messages'][-1]
            if (
                isinstance(message, AIMessage)
                and message.tool_calls
                and len(message.tool_calls) > 0
            ):
                tool_call = message.tool_calls[0]
                tool_name = tool_call.get("name")
                tool_args = tool_call.get("args")
                yield {
                    "is_task_complete": False,
                    "require_user_input": False,
                    "content": f'正在使用{tool_name}工具执行操作，请稍等...，参数：{tool_args}',
                }
            elif isinstance(message, ToolMessage):
                try:
                    content_dict = json.loads(message.content)
                    if isinstance(content_dict, dict) and content_dict.get('type') == 'formSchema':
                        yield {
                            "is_task_complete": False,
                            "require_user_input": True,
                            "content": "请选择返回的表单，然后点击提交按钮...",
                            "formSchema": content_dict
                        }
                    elif isinstance(content_dict, dict) and content_dict.get('type') == 'error':
                        yield {
                            "is_task_complete": False,
                            "require_user_input": True,
                            "content": content_dict.get('message', '操作失败'),
                        }
                    else:
                        yield {
                            "is_task_complete": False,
                            "require_user_input": False,
                            "content": "正在处理工具返回的工具调用结果，请稍等...",
                        }
                except (json.JSONDecodeError, AttributeError):
                    pass
        yield self.get_agent_response(config)

    def get_agent_response(self, config: RunnableConfig) -> dict[str, Any]:
        current_state = self.graph.get_state(config)

        structured_response = current_state.values.get('structured_response')
        if structured_response and isinstance(
            structured_response, ResponseFormat
        ):
            if structured_response.status in {'input_required', 'error'}:
                return {
                    'is_task_complete': False,
                    'require_user_input': True,
                    'content': structured_response.message,
                }
            if structured_response.status == 'completed':
                return {
                    'is_task_complete': True,
                    'require_user_input': False,
                    'content': structured_response.message,
                }
        
        # 检查最后一条消息，看是否是工具调用的结果
        messages = current_state.values.get('messages', [])
        if messages:
            last_message = messages[-1]

            # 如果最后一条消息是工具消息，尝试解析其内容
            if isinstance(last_message, ToolMessage):
                try:
                    content_dict = json.loads(last_message.content)
                    if isinstance(content_dict, dict) and content_dict.get('type') == 'formSchema':
                        return {
                            "is_task_complete": False,
                            "require_user_input": True,
                            "content": "请选择分支和流水线",
                            "formSchema": content_dict
                        }
                    elif isinstance(content_dict, dict) and content_dict.get('type') == 'error':
                        return {
                            "is_task_complete": False,
                            "require_user_input": True,
                            "content": content_dict.get('message', '操作失败')
                        }
                except (json.JSONDecodeError, AttributeError):
                    pass

            # 如果最后一条消息是 AI 消息，返回其内容
            elif isinstance(last_message, AIMessage):
                return {
                    "is_task_complete": True,
                    "require_user_input": False,
                    "content": last_message.content
                }

        return {
            'is_task_complete': False,
            'require_user_input': True,
            'content': 'We are unable to process your request at the moment. Please try again.',
        }

    SUPPORTED_CONTENT_TYPES = ['text', 'text/plain']
