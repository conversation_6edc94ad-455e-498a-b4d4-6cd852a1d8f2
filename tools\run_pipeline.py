import requests
import json
from typing import Any, List

from langchain_core.tools import tool

from .cicd import HEADERS, BASE_URL, cicd_search, generate_select_service_form

def get_app_branch_impl(app_id: str) -> List[str] | str:
    """实际的分支查询实现"""
    try:
        # 确保app_id是字符串格式
        app_id_str = str(app_id)
        response = requests.get(
                f"{BASE_URL}app/apps/{app_id_str}/branches?regex=.*",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        branches = result.get("data", [])
        return branches
    except Exception as e:
        return f"查询失败: {str(e)}"

@tool
def get_app_branch(app_id: str) -> List[str] | str:
    """
    use this to get app branch list.
    Args:
        app_id: 服务id (字符串格式).
    Returns:
        app branch list
    """
    return get_app_branch_impl(app_id)

def get_app_pipeline_impl(app_id: str, project_id: str) -> List[Any] | str:
    """实际的流水线查询实现"""
    try:
        # 确保参数是字符串格式
        app_id_str = str(app_id)
        project_id_str = str(project_id)
        response = requests.get(
                f"{BASE_URL}pipeline/pipelines?appId={app_id_str}&projectId={project_id_str}",
            headers=HEADERS,
            timeout=10,
        )
        result = response.json()
        data = result.get("data", [])
        if isinstance(data, dict):
            pipelines = data.get("list", [])
        else:
            pipelines = data if isinstance(data, list) else []
        return pipelines
    except Exception as e:
        return f"查询失败: {str(e)}"

@tool
def get_app_pipeline(app_id: str, project_id: str) -> List[Any] | str:
    """
    use this to get app pipeline list.
    Args:
        app_id: 服务id (字符串格式).
        project_id: 项目id (字符串格式).
    Returns:
        app pipeline list
    """
    return get_app_pipeline_impl(app_id, project_id)

@tool
def run_pipeline(pipeline_id: int, branch: str, description: str = "agent触发") -> List[Any] | str:
    """
    use this to run app pipeline.
    Args:
        pipeline_id: 流水线id (字符串格式).
        branch: 分支名称.
    Returns:
        run pipeline result
    """
    try:
        userEmail = "<EMAIL>"
        # 确保pipeline_id是字符串格式
        response = requests.post(
                f"{BASE_URL}pipeline/pipelines/openapi/run",
            headers=HEADERS,
            json={
                "pipelineId": pipeline_id,
                "branch": branch,
                "description": description,
                "userEmail": userEmail
            },
            timeout=10,
        )
        result = response.json()
        return result
    except Exception as e:
        return f"执行失败: {str(e)}"

@tool
def prepare_pipeline_execution(service_name: str) -> str:
    """
    准备流水线执行，获取服务信息、分支列表和流水线列表
    Args:
        service_name: 服务名称或包含"用户已选择服务ID="的消息
    Returns:
        包含服务信息、分支列表、流水线列表的JSON格式表单，供用户选择
    """
    try:
        if not service_name:
            return json.dumps({"type": "error", "message": "请提供服务名称"})
        # 提取服务ID或使用服务名称
        service_id = None
        if "用户已选择服务ID=" in service_name:
            service_id = service_name.split("用户已选择服务ID=")[1].strip()
            response = requests.get(
                f"{BASE_URL}app/apps/{service_id}",
                headers=HEADERS,
                timeout=10,
            )
            result = response.json()
            service_info = result.get("data", {})
            if not service_info:
                return json.dumps({"type": "error", "message": f"无法获取服务ID为{service_id}的信息"})
        else:
            service_info = cicd_search(service_name)
            
            if isinstance(service_info, str) and "查询到多个服务" in service_info:
                # 提取服务列表并创建选择表单
                import re
                import ast
                match = re.search(r'\[(.*)\]', service_info)
                if match:
                    services_str = "[" + match.group(1) + "]"
                    services_list = ast.literal_eval(services_str)
                    
                    return generate_select_service_form(services_list)
                else:
                    return json.dumps({"type": "error", "message": "无法提取服务列表"})
            if isinstance(service_info, str):
                return json.dumps({"type": "error", "message": service_info})
            
            service_id = service_info.get('id')
            
        # 获取项目ID - 考虑驼峰命名的情况
        project_id = service_info.get('projectID') or service_info.get('projectId')
        if not service_id or not project_id:
            return json.dumps({"type": "error", "message": "无法获取服务ID或项目ID"})

        # 获取分支列表
        branches = get_app_branch_impl(str(service_id))
        if isinstance(branches, str) or not branches:
            return json.dumps({"type": "error", "message": "获取分支失败或无分支"})

        # 获取流水线列表
        pipelines = get_app_pipeline_impl(str(service_id), str(project_id))
        if isinstance(pipelines, str) or not pipelines:
            return json.dumps({"type": "error", "message": "获取流水线失败或无流水线"})

        return generate_pipeline_execution_form(service_info.get('name', service_name), branches, pipelines)

    except Exception as e:
        return json.dumps({"type": "error", "message": f"准备流水线执行失败: {str(e)}"})

def generate_pipeline_execution_form(service_name: str = "", branches: List[str] = [], pipelines: List[str] = []):
    pipeline_ids = [pipeline['id'] for pipeline in pipelines] if pipelines else []
    pipeline_names = [pipeline.get('name', '') for pipeline in pipelines] if pipelines else []
    form_dict = {
        "type": "formSchema",
        "schema": {
            "title": f'运行{service_name}服务流水线',
            "type": "object",
            "required": [
                "branch",
                "pipeline_id"
            ],
            "properties": {
                "branch": {
                    "type": "string", 
                    "title": "分支选择",
                    "enum": branches if isinstance(branches, list) else []
                },
                "pipeline_id": {
                    "type": "number",
                    "title": "流水线选择",
                    "enum": pipeline_ids
                },
                "description": {
                    "type": "string",
                    "title": "运行描述"
                }
            },
        },
        "uiSchema": {
            "branch": {
                "ui:description": "分支选择"
            },
            "pipeline_id": {
                "ui:description": "流水线选择",
                "ui:enumNames": pipeline_names
            },
            "description": {
                "ui:description": "运行描述",
                "ui:widget": "textarea",
                "ui:placeholder": "请输入运行描述",
            }
        },
        "formData": {
            "branch": "",
            "pipeline_id": 0,
            "description": ""
        },
        "instructions": "请选择分支和流水线",
    }

    return json.dumps(form_dict)
    
